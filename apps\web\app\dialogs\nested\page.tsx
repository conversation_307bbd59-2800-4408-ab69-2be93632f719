"use client";

import { useState } from "react";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";

export default function Page() {
  const [open1, setOpen1] = useState(true);
  const [open2, setOpen2] = useState(false);
  const [open3, setOpen3] = useState(false);
  const [open4, setOpen4] = useState(false);

  return (
    <Dialog open={open1} onOpenChange={setOpen1}>
      <DialogContent className="sm:max-w-[520px]">
        <DialogHeader>
          <DialogTitle>{"1. Seviye Diyalog"}</DialogTitle>
          <DialogDescription>
            {"Bu sayfa iç içe geçmiş diyalogları (nested dialogs) gösterir. Toplam 4 seviye vardır."}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="primary" onClick={() => setOpen2(true)}>
            {"2. Seviyeyi Aç"}
          </Button>
        </DialogFooter>

        {/* Level 2 */}
        <Dialog open={open2} onOpenChange={setOpen2}>
          <DialogContent className="sm:max-w-[480px]">
            <DialogHeader>
              <DialogTitle>{"2. Seviye Diyalog"}</DialogTitle>
              <DialogDescription>
                {"Üstteki diyaloğun içinde açıldı. Bir seviye daha açabilirsiniz."}
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="gap-2">
              <DialogClose asChild>
                <Button variant="secondary">{"2. Seviyeyi Kapat"}</Button>
              </DialogClose>
              <Button variant="primary" onClick={() => setOpen3(true)}>
                {"3. Seviyeyi Aç"}
              </Button>
            </DialogFooter>

            {/* Level 3 */}
            <Dialog open={open3} onOpenChange={setOpen3}>
              <DialogContent className="sm:max-w-[440px]">
                <DialogHeader>
                  <DialogTitle>{"3. Seviye Diyalog"}</DialogTitle>
                  <DialogDescription>
                    {"Bir seviye daha içe gidebilirsiniz. Sonraki seviye son seviyedir."}
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter className="gap-2">
                  <DialogClose asChild>
                    <Button variant="secondary">{"3. Seviyeyi Kapat"}</Button>
                  </DialogClose>
                  <Button variant="primary" onClick={() => setOpen4(true)}>
                    {"4. Seviyeyi Aç"}
                  </Button>
                </DialogFooter>

                {/* Level 4 */}
                <Dialog open={open4} onOpenChange={setOpen4}>
                  <DialogContent className="sm:max-w-[400px]">
                    <DialogHeader>
                      <DialogTitle>{"4. Seviye Diyalog"}</DialogTitle>
                      <DialogDescription>
                        {"Bu son seviye. Kapatınca bir önceki seviyeye geri dönersiniz."}
                      </DialogDescription>
                    </DialogHeader>
                    <DialogFooter className="gap-2">
                      <DialogClose asChild>
                        <Button variant="secondary">{"4. Seviyeyi Kapat"}</Button>
                      </DialogClose>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </DialogContent>
            </Dialog>
          </DialogContent>
        </Dialog>
      </DialogContent>
    </Dialog>
  );
}

